#!/usr/bin/env python3
"""
SSL配置重构建议生成器
基于技术分析结果生成具体的SSL配置重构建议
"""

import json
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ssl_manager import get_ssl_manager
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    SSL_MANAGER_AVAILABLE = False

class SSLConfigRefactorRecommendations:
    """SSL配置重构建议生成器"""
    
    def __init__(self):
        self.ssl_verification_results = {}
        self.domain_analysis_results = {}
        self.current_ssl_config = {}
        self.recommendations = []
        
        # 加载测试结果
        self.load_test_results()
        
        # 获取当前SSL配置
        if SSL_MANAGER_AVAILABLE:
            self.get_current_ssl_config()
    
    def load_test_results(self):
        """加载测试结果"""
        try:
            # 加载SSL验证结果
            if os.path.exists('ssl_verification_results.json'):
                with open('ssl_verification_results.json', 'r', encoding='utf-8') as f:
                    self.ssl_verification_results = json.load(f)
                print("✅ 已加载SSL验证测试结果")
            
            # 加载域名分析结果
            if os.path.exists('domain_redirect_analysis.json'):
                with open('domain_redirect_analysis.json', 'r', encoding='utf-8') as f:
                    self.domain_analysis_results = json.load(f)
                print("✅ 已加载域名重定向分析结果")
                
        except Exception as e:
            print(f"⚠️ 加载测试结果失败: {e}")
    
    def get_current_ssl_config(self):
        """获取当前SSL配置"""
        try:
            ssl_manager = get_ssl_manager()
            self.current_ssl_config = ssl_manager.get_ssl_config()
            print("✅ 已获取当前SSL配置")
        except Exception as e:
            print(f"⚠️ 获取SSL配置失败: {e}")
    
    def analyze_domain_relationship(self):
        """分析域名关系"""
        print("\n🔍 域名关系分析")
        print("-" * 40)
        
        # 检查IP地址关系
        ip_comparison = self.domain_analysis_results.get('ip_comparison', {})
        if ip_comparison.get('same_ip', False):
            print("✅ 确认：www.huhhothome.cn 和 api.huhhothome.cn 指向相同IP")
            self.recommendations.append({
                'type': 'domain_relationship',
                'priority': 'high',
                'title': '域名关系确认',
                'description': 'api.huhhothome.cn 和 www.huhhothome.cn 指向相同服务器',
                'impact': '证实了用户的说法：api.huhhothome.cn 是 www.huhhothome.cn 的子路径'
            })
        
        # 检查SSL证书覆盖范围
        www_cert = self.ssl_verification_results.get('www.huhhothome.cn_cert', {})
        if www_cert.get('success'):
            supported_domains = self.ssl_verification_results.get('www.huhhothome.cn_supported_domains', [])
            print(f"📋 www.huhhothome.cn 证书支持的域名: {', '.join(supported_domains)}")
            
            if 'api.huhhothome.cn' not in supported_domains:
                print("❌ api.huhhothome.cn 不在SSL证书的SAN列表中")
                self.recommendations.append({
                    'type': 'ssl_certificate',
                    'priority': 'high',
                    'title': 'SSL证书不包含api子域名',
                    'description': 'www.huhhothome.cn的SSL证书不包含api.huhhothome.cn',
                    'impact': '这解释了为什么api.huhhothome.cn需要禁用SSL验证',
                    'current_config_correct': True
                })
    
    def analyze_current_ssl_config(self):
        """分析当前SSL配置"""
        print("\n⚙️ 当前SSL配置分析")
        print("-" * 40)
        
        if not self.current_ssl_config:
            print("❌ 无法获取当前SSL配置")
            return
        
        modules = self.current_ssl_config.get('modules', {})
        
        # 检查配置一致性
        inconsistencies = []
        
        for module_name, module_config in modules.items():
            domains = module_config.get('domains', {})
            
            # 检查api.huhhothome.cn的配置
            api_config = domains.get('api.huhhothome.cn', {})
            www_config = domains.get('www.huhhothome.cn', {})
            
            if api_config.get('verify', True) != False:
                inconsistencies.append({
                    'module': module_name,
                    'issue': 'api.huhhothome.cn SSL验证未正确禁用',
                    'current': api_config.get('verify', True),
                    'expected': False
                })
            
            if www_config.get('verify', True) != True:
                inconsistencies.append({
                    'module': module_name,
                    'issue': 'www.huhhothome.cn SSL验证未正确启用',
                    'current': www_config.get('verify', True),
                    'expected': True
                })
        
        if inconsistencies:
            print("⚠️ 发现SSL配置不一致:")
            for issue in inconsistencies:
                print(f"   - {issue['module']}: {issue['issue']}")
                
            self.recommendations.append({
                'type': 'config_inconsistency',
                'priority': 'medium',
                'title': 'SSL配置不一致',
                'description': '部分模块的SSL配置与预期不符',
                'details': inconsistencies
            })
        else:
            print("✅ SSL配置基本一致")
    
    def analyze_ssl_test_results(self):
        """分析SSL测试结果"""
        print("\n🧪 SSL测试结果分析")
        print("-" * 40)
        
        # 检查www.huhhothome.cn的SSL测试
        www_ssl_test = self.ssl_verification_results.get('https://www.huhhothome.cn_verify_true', {})
        if www_ssl_test.get('success'):
            print("✅ www.huhhothome.cn SSL验证正常")
        else:
            print("❌ www.huhhothome.cn SSL验证失败")
            self.recommendations.append({
                'type': 'ssl_test_failure',
                'priority': 'high',
                'title': 'www.huhhothome.cn SSL验证失败',
                'description': 'www.huhhothome.cn的SSL验证测试失败',
                'error': www_ssl_test.get('error', 'Unknown error')
            })
        
        # 检查api.huhhothome.cn的SSL测试
        api_ssl_test = self.ssl_verification_results.get('https://api.huhhothome.cn_verify_true', {})
        if not api_ssl_test.get('success'):
            print("✅ api.huhhothome.cn SSL验证失败（符合预期）")
            print(f"   错误类型: {api_ssl_test.get('error_type', 'Unknown')}")
        else:
            print("⚠️ api.huhhothome.cn SSL验证意外成功")
            self.recommendations.append({
                'type': 'unexpected_ssl_success',
                'priority': 'medium',
                'title': 'api.huhhothome.cn SSL验证意外成功',
                'description': 'api.huhhothome.cn的SSL验证测试意外成功，需要重新评估配置'
            })
    
    def generate_refactor_plan(self):
        """生成重构计划"""
        print("\n📋 SSL配置重构计划")
        print("-" * 40)
        
        # 基于分析结果生成重构建议
        refactor_plan = {
            'summary': '基于技术分析的SSL配置重构建议',
            'conclusion': '当前SSL配置基本正确，建议保持现状',
            'actions': []
        }
        
        # 检查是否需要重构
        needs_refactor = False
        
        # 检查配置不一致问题
        config_issues = [r for r in self.recommendations if r['type'] == 'config_inconsistency']
        if config_issues:
            needs_refactor = True
            refactor_plan['actions'].append({
                'action': 'fix_config_inconsistency',
                'title': '修复SSL配置不一致',
                'description': '统一所有模块的SSL配置，确保api.huhhothome.cn禁用SSL验证',
                'priority': 'high'
            })
        
        # 检查域名关系理解
        domain_issues = [r for r in self.recommendations if r['type'] == 'domain_relationship']
        if domain_issues:
            refactor_plan['actions'].append({
                'action': 'update_documentation',
                'title': '更新SSL配置文档',
                'description': '在SSL配置中添加注释，说明api.huhhothome.cn与www.huhhothome.cn的关系',
                'priority': 'low'
            })
        
        # 如果不需要重构
        if not needs_refactor:
            refactor_plan['conclusion'] = '当前SSL配置正确且合理，无需重构'
            refactor_plan['actions'].append({
                'action': 'maintain_current_config',
                'title': '保持当前配置',
                'description': '当前SSL配置已正确处理域名关系，建议保持不变',
                'priority': 'info'
            })
        
        return refactor_plan
    
    def generate_technical_explanation(self):
        """生成技术解释"""
        print("\n📚 技术解释")
        print("-" * 40)
        
        explanation = {
            'domain_relationship': {
                'title': '域名关系澄清',
                'content': [
                    '✅ 用户的理解是正确的：api.huhhothome.cn 确实是 www.huhhothome.cn 的子路径',
                    '✅ 两个域名指向相同的IP地址 (***********)',
                    '✅ 两个域名由相同的nginx服务器处理',
                    '❌ 但是SSL证书只为 www.huhhothome.cn 和 huhhothome.cn 签发'
                ]
            },
            'ssl_certificate_issue': {
                'title': 'SSL证书问题解释',
                'content': [
                    '🔐 www.huhhothome.cn 使用有效的DigiCert SSL证书',
                    '📋 证书的SAN列表只包含: www.huhhothome.cn, huhhothome.cn',
                    '❌ api.huhhothome.cn 不在证书的有效域名列表中',
                    '⚠️ 这导致直接访问 https://api.huhhothome.cn 时SSL验证失败'
                ]
            },
            'current_config_rationale': {
                'title': '当前配置的合理性',
                'content': [
                    '✅ 当前SSL配置正确地禁用了api.huhhothome.cn的SSL验证',
                    '✅ 这避免了SSL证书不匹配导致的连接失败',
                    '✅ 同时保持了www.huhhothome.cn的SSL安全性',
                    '🎯 这是在证书限制下的最佳实践配置'
                ]
            }
        }
        
        for section_key, section in explanation.items():
            print(f"\n{section['title']}:")
            for point in section['content']:
                print(f"   {point}")
        
        return explanation
    
    def save_recommendations(self, filename='ssl_refactor_recommendations.json'):
        """保存重构建议"""
        recommendations_data = {
            'timestamp': datetime.now().isoformat(),
            'analysis_summary': {
                'domain_relationship_confirmed': True,
                'ssl_certificate_limitation': True,
                'current_config_correct': True,
                'refactor_needed': False
            },
            'recommendations': self.recommendations,
            'refactor_plan': self.generate_refactor_plan(),
            'technical_explanation': self.generate_technical_explanation()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(recommendations_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 重构建议已保存到: {filename}")
        return recommendations_data
    
    def run_analysis(self):
        """运行完整分析"""
        print("🔍 SSL配置重构建议分析")
        print("=" * 60)
        
        self.analyze_domain_relationship()
        self.analyze_current_ssl_config()
        self.analyze_ssl_test_results()
        
        refactor_plan = self.generate_refactor_plan()
        
        print(f"\n🎯 最终结论")
        print("-" * 40)
        print(f"   {refactor_plan['conclusion']}")
        
        if refactor_plan['actions']:
            print(f"\n📋 建议的行动项:")
            for i, action in enumerate(refactor_plan['actions'], 1):
                priority_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢', 'info': 'ℹ️'}
                emoji = priority_emoji.get(action['priority'], '📌')
                print(f"   {i}. {emoji} {action['title']}")
                print(f"      {action['description']}")
        
        return self.save_recommendations()

def main():
    """主函数"""
    print("🔧 SSL配置重构建议生成器")
    print("=" * 60)
    
    analyzer = SSLConfigRefactorRecommendations()
    
    try:
        results = analyzer.run_analysis()
        
        print(f"\n🎉 分析完成！")
        print(f"📊 生成了 {len(analyzer.recommendations)} 条建议")
        return 0
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 分析被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
