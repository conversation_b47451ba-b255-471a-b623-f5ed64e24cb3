#!/usr/bin/env python3
"""
SSL配置不一致修复脚本
修复cookie_tester、login_helper、notifications模块中api.huhhothome.cn的SSL配置
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ssl_manager import get_ssl_manager
    from config.unified_config import get_config
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    SSL_MANAGER_AVAILABLE = False
    print("❌ SSL管理器不可用")
    sys.exit(1)

class SSLConfigFixer:
    """SSL配置修复器"""

    def __init__(self):
        # 获取统一配置管理器
        self.config_manager = get_config()
        # 使用配置管理器初始化SSL管理器
        self.ssl_manager = get_ssl_manager(self.config_manager)
        self.fixes_applied = []
        self.backup_config = None

    def backup_current_config(self):
        """备份当前配置"""
        try:
            self.backup_config = self.ssl_manager.get_ssl_config()

            # 保存备份到文件
            backup_filename = f"ssl_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(self.backup_config, f, indent=2, ensure_ascii=False, default=str)

            print(f"✅ 配置已备份到: {backup_filename}")
            return True

        except Exception as e:
            print(f"❌ 配置备份失败: {e}")
            return False

    def check_current_config(self):
        """检查当前配置状态"""
        print("\n🔍 检查当前SSL配置状态")
        print("-" * 40)

        current_config = self.ssl_manager.get_ssl_config()
        modules = current_config.get('modules', {})

        issues = []

        # 需要修复的模块
        modules_to_check = ['cookie_tester', 'login_helper', 'notifications']

        for module_name in modules_to_check:
            module_config = modules.get(module_name, {})
            domains = module_config.get('domains', {})
            api_config = domains.get('api.huhhothome.cn', {})

            current_verify = api_config.get('verify', True)  # 默认为True

            if current_verify != False:
                issues.append({
                    'module': module_name,
                    'current_verify': current_verify,
                    'expected_verify': False,
                    'needs_fix': True
                })
                print(f"❌ {module_name}: api.huhhothome.cn SSL验证 = {current_verify} (应为 False)")
            else:
                print(f"✅ {module_name}: api.huhhothome.cn SSL验证 = {current_verify}")

        return issues

    def apply_ssl_config_fixes(self, issues):
        """应用SSL配置修复"""
        if not issues:
            print("\n✅ 无需修复，所有配置都正确")
            return True

        print(f"\n🔧 开始修复SSL配置")
        print("-" * 40)

        try:
            # 构建配置更新
            config_updates = {
                'modules': {}
            }

            for issue in issues:
                module_name = issue['module']

                # 确保模块配置存在
                if module_name not in config_updates['modules']:
                    config_updates['modules'][module_name] = {
                        'domains': {}
                    }

                # 设置api.huhhothome.cn的SSL验证为False
                config_updates['modules'][module_name]['domains']['api.huhhothome.cn'] = {
                    'verify': False,
                    'reason': 'SSL证书不包含此域名，已通过技术分析确认'
                }

                print(f"🔧 修复 {module_name}: 设置 api.huhhothome.cn SSL验证 = False")

                self.fixes_applied.append({
                    'module': module_name,
                    'action': 'set_api_ssl_verify_false',
                    'timestamp': datetime.now().isoformat()
                })

            # 应用配置更新
            self.ssl_manager.update_ssl_config(config_updates)

            print("✅ SSL配置修复完成")
            return True

        except Exception as e:
            print(f"❌ SSL配置修复失败: {e}")
            return False

    def verify_fixes(self):
        """验证修复结果"""
        print(f"\n🧪 验证修复结果")
        print("-" * 40)

        # 重新检查配置
        issues = self.check_current_config()

        if not issues:
            print("✅ 所有SSL配置问题已修复")
            return True
        else:
            print(f"❌ 仍有 {len(issues)} 个配置问题未解决")
            return False

    def test_ssl_functionality(self):
        """测试SSL功能"""
        print(f"\n🧪 测试SSL功能")
        print("-" * 40)

        modules_to_test = ['cookie_tester', 'login_helper']

        for module in modules_to_test:
            try:
                # 测试www.huhhothome.cn (应该启用SSL验证)
                www_verify = self.ssl_manager.should_verify_ssl(module, 'www.huhhothome.cn')
                www_param = self.ssl_manager.get_requests_verify_param(module, 'www.huhhothome.cn')

                # 测试api.huhhothome.cn (应该禁用SSL验证)
                api_verify = self.ssl_manager.should_verify_ssl(module, 'api.huhhothome.cn')
                api_param = self.ssl_manager.get_requests_verify_param(module, 'api.huhhothome.cn')

                print(f"   {module}:")
                print(f"     www.huhhothome.cn: verify={www_verify}, param={www_param}")
                print(f"     api.huhhothome.cn: verify={api_verify}, param={api_param}")

                # 验证配置正确性
                if www_verify == True and api_verify == False:
                    print(f"     ✅ {module} SSL配置正确")
                else:
                    print(f"     ❌ {module} SSL配置异常")

            except Exception as e:
                print(f"     ❌ {module} 测试失败: {e}")

    def generate_fix_report(self):
        """生成修复报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': self.fixes_applied,
            'total_fixes': len(self.fixes_applied),
            'backup_config_available': self.backup_config is not None,
            'status': 'completed' if self.fixes_applied else 'no_fixes_needed'
        }

        report_filename = f"ssl_config_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n📊 修复报告已保存到: {report_filename}")
        return report

    def run_fix_process(self):
        """运行完整的修复流程"""
        print("🔧 SSL配置不一致修复工具")
        print("=" * 60)

        # 1. 备份当前配置
        if not self.backup_current_config():
            print("❌ 配置备份失败，中止修复流程")
            return False

        # 2. 检查当前配置
        issues = self.check_current_config()

        if not issues:
            print("\n✅ 当前SSL配置完全正确，无需修复")
            return True

        print(f"\n⚠️ 发现 {len(issues)} 个SSL配置问题需要修复")

        # 3. 应用修复
        if not self.apply_ssl_config_fixes(issues):
            print("❌ SSL配置修复失败")
            return False

        # 4. 验证修复结果
        if not self.verify_fixes():
            print("❌ 修复验证失败")
            return False

        # 5. 测试SSL功能
        self.test_ssl_functionality()

        # 6. 生成修复报告
        self.generate_fix_report()

        print(f"\n🎉 SSL配置修复完成！")
        print(f"📊 共修复了 {len(self.fixes_applied)} 个配置问题")

        return True

def main():
    """主函数"""
    if not SSL_MANAGER_AVAILABLE:
        print("❌ SSL管理器不可用，无法执行修复")
        return 1

    fixer = SSLConfigFixer()

    try:
        success = fixer.run_fix_process()
        return 0 if success else 1

    except KeyboardInterrupt:
        print(f"\n⏹️ 修复过程被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
