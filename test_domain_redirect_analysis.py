#!/usr/bin/env python3
"""
域名重定向关系分析脚本
验证 api.huhhothome.cn 是否重定向到 www.huhhothome.cn
"""

import requests
import socket
import sys
import os
import json
from urllib.parse import urlparse, urljoin
import subprocess

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DomainRedirectAnalyzer:
    """域名重定向分析器"""

    def __init__(self):
        self.results = {}
        self.session = requests.Session()
        self.session.verify = False  # 禁用SSL验证以便测试重定向
        self.session.allow_redirects = False  # 手动处理重定向

    def resolve_dns(self, domain):
        """解析DNS记录（使用socket.gethostbyname）"""
        try:
            print(f"🔍 解析DNS: {domain}")

            # 使用socket获取IP地址
            try:
                ip = socket.gethostbyname(domain)
                a_ips = [ip]
            except:
                a_ips = []

            # 简化版本，不支持CNAME查询
            cnames = []

            return {
                'success': True,
                'a_records': a_ips,
                'cname_records': cnames
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def test_http_redirect(self, url, max_redirects=10):
        """测试HTTP重定向链"""
        try:
            print(f"🌐 测试重定向: {url}")

            redirect_chain = []
            current_url = url

            for i in range(max_redirects):
                response = self.session.get(current_url, timeout=10)

                redirect_info = {
                    'step': i + 1,
                    'url': current_url,
                    'status_code': response.status_code,
                    'headers': dict(response.headers),
                    'final': False
                }

                redirect_chain.append(redirect_info)

                # 检查是否是重定向状态码
                if response.status_code in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location')
                    if location:
                        # 处理相对URL
                        if location.startswith('/'):
                            parsed = urlparse(current_url)
                            current_url = f"{parsed.scheme}://{parsed.netloc}{location}"
                        else:
                            current_url = location
                        print(f"   → 重定向到: {current_url}")
                    else:
                        break
                else:
                    # 非重定向状态码，结束
                    redirect_info['final'] = True
                    break

            return {
                'success': True,
                'redirect_chain': redirect_chain,
                'final_url': current_url,
                'total_redirects': len(redirect_chain) - 1
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }

    def test_ip_comparison(self, domain1, domain2):
        """比较两个域名的IP地址"""
        try:
            print(f"🔍 比较IP地址: {domain1} vs {domain2}")

            ip1 = socket.gethostbyname(domain1)
            ip2 = socket.gethostbyname(domain2)

            print(f"   {domain1}: {ip1}")
            print(f"   {domain2}: {ip2}")

            return {
                'success': True,
                'domain1_ip': ip1,
                'domain2_ip': ip2,
                'same_ip': ip1 == ip2
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def test_server_headers(self, url):
        """测试服务器响应头"""
        try:
            print(f"🔍 检查服务器头: {url}")

            response = self.session.get(url, timeout=10)

            return {
                'success': True,
                'status_code': response.status_code,
                'server': response.headers.get('Server', 'Unknown'),
                'headers': dict(response.headers),
                'final_url': response.url
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def analyze_certificate_error(self, domain):
        """分析SSL证书错误的详细信息"""
        try:
            print(f"🔍 分析SSL证书错误: {domain}")

            # 尝试获取证书信息（忽略验证）
            import ssl
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            with socket.create_connection((domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()

            # 分析证书中的域名
            subject_cn = None
            for subject in cert.get('subject', []):
                for key, value in subject:
                    if key == 'commonName':
                        subject_cn = value
                        break

            san_domains = []
            for san_type, san_value in cert.get('subjectAltName', []):
                if san_type == 'DNS':
                    san_domains.append(san_value)

            return {
                'success': True,
                'certificate_cn': subject_cn,
                'san_domains': san_domains,
                'domain_in_cert': domain in san_domains or domain == subject_cn,
                'certificate_serial': cert.get('serialNumber'),
                'issuer': dict(x[0] for x in cert.get('issuer', []))
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def run_comprehensive_analysis(self):
        """运行综合域名分析"""
        print("🚀 开始域名重定向关系分析")
        print("=" * 60)

        domains = ['www.huhhothome.cn', 'api.huhhothome.cn', 'huhhothome.cn']

        # 1. DNS解析分析
        print(f"\n📋 DNS解析分析")
        print("-" * 40)

        for domain in domains:
            dns_result = self.resolve_dns(domain)
            self.results[f'{domain}_dns'] = dns_result

            if dns_result['success']:
                print(f"   {domain}:")
                if dns_result['a_records']:
                    print(f"     A记录: {', '.join(dns_result['a_records'])}")
                if dns_result['cname_records']:
                    print(f"     CNAME: {', '.join(dns_result['cname_records'])}")
            else:
                print(f"   {domain}: DNS解析失败 - {dns_result['error']}")

        # 2. IP地址比较
        print(f"\n🔍 IP地址比较")
        print("-" * 40)

        ip_comparison = self.test_ip_comparison('www.huhhothome.cn', 'api.huhhothome.cn')
        self.results['ip_comparison'] = ip_comparison

        if ip_comparison['success']:
            if ip_comparison['same_ip']:
                print("✅ 两个域名指向相同IP地址")
            else:
                print("❌ 两个域名指向不同IP地址")

        # 3. HTTP重定向测试
        print(f"\n🌐 HTTP重定向测试")
        print("-" * 40)

        test_urls = [
            'http://api.huhhothome.cn',
            'https://api.huhhothome.cn',
            'http://www.huhhothome.cn',
            'https://www.huhhothome.cn'
        ]

        for url in test_urls:
            redirect_result = self.test_http_redirect(url)
            self.results[f'{url}_redirect'] = redirect_result

            if redirect_result['success']:
                if redirect_result['total_redirects'] > 0:
                    print(f"   {url}: {redirect_result['total_redirects']}次重定向 → {redirect_result['final_url']}")
                else:
                    print(f"   {url}: 无重定向")
            else:
                print(f"   {url}: 测试失败 - {redirect_result['error']}")

        # 4. 服务器响应头分析
        print(f"\n🔍 服务器响应头分析")
        print("-" * 40)

        for url in ['http://www.huhhothome.cn', 'http://api.huhhothome.cn']:
            server_result = self.test_server_headers(url)
            self.results[f'{url}_server'] = server_result

            if server_result['success']:
                print(f"   {url}:")
                print(f"     服务器: {server_result['server']}")
                print(f"     状态码: {server_result['status_code']}")
            else:
                print(f"   {url}: 分析失败 - {server_result['error']}")

        # 5. SSL证书错误分析
        print(f"\n🔐 SSL证书错误分析")
        print("-" * 40)

        cert_analysis = self.analyze_certificate_error('api.huhhothome.cn')
        self.results['api_cert_analysis'] = cert_analysis

        if cert_analysis['success']:
            print(f"   api.huhhothome.cn 使用的证书:")
            print(f"     CN: {cert_analysis['certificate_cn']}")
            print(f"     SAN域名: {', '.join(cert_analysis['san_domains'])}")
            print(f"     域名匹配: {'✅' if cert_analysis['domain_in_cert'] else '❌'}")
        else:
            print(f"   证书分析失败: {cert_analysis['error']}")

        return self.results

    def generate_technical_analysis(self):
        """生成技术分析报告"""
        print(f"\n📊 技术分析报告")
        print("-" * 40)

        # 分析IP地址关系
        ip_comp = self.results.get('ip_comparison', {})
        if ip_comp.get('success') and ip_comp.get('same_ip'):
            print("✅ 域名关系确认:")
            print("   - www.huhhothome.cn 和 api.huhhothome.cn 指向相同IP")
            print("   - 这证实了它们是同一服务器的不同子域名")

        # 分析重定向行为
        api_http_redirect = self.results.get('http://api.huhhothome.cn_redirect', {})
        if api_http_redirect.get('success'):
            if api_http_redirect.get('total_redirects', 0) > 0:
                final_url = api_http_redirect.get('final_url', '')
                if 'www.huhhothome.cn' in final_url:
                    print("✅ 重定向确认:")
                    print(f"   - api.huhhothome.cn 重定向到 www.huhhothome.cn")
                    print("   - 这进一步证实了域名的包含关系")

        # 分析SSL证书
        cert_analysis = self.results.get('api_cert_analysis', {})
        if cert_analysis.get('success'):
            if not cert_analysis.get('domain_in_cert', False):
                print("⚠️ SSL证书分析:")
                print("   - api.huhhothome.cn 不在当前SSL证书的有效域名列表中")
                print("   - 证书主要为 www.huhhothome.cn 和 huhhothome.cn 签发")
                print("   - 这解释了为什么需要禁用SSL验证")

        # 生成配置建议
        print(f"\n💡 配置建议")
        print("-" * 40)

        same_ip = ip_comp.get('same_ip', False)
        has_redirect = any('redirect' in k and v.get('total_redirects', 0) > 0
                          for k, v in self.results.items()
                          if 'api.huhhothome.cn' in k)

        if same_ip and has_redirect:
            print("🎯 推荐方案：URL重写而非SSL配置修改")
            print("   1. 在应用层将 api.huhhothome.cn 的请求重写为 www.huhhothome.cn")
            print("   2. 保持当前SSL配置不变（api.huhhothome.cn禁用SSL验证）")
            print("   3. 这样既保证了功能正常，又避免了SSL证书问题")
        else:
            print("🔧 当前配置合理：")
            print("   - api.huhhothome.cn 确实需要特殊的SSL处理")
            print("   - 建议保持现有的SSL配置策略")

    def save_results(self, filename='domain_redirect_analysis.json'):
        """保存分析结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n💾 分析结果已保存到: {filename}")

def main():
    """主函数"""
    print("🔍 域名重定向关系分析工具")
    print("=" * 60)

    analyzer = DomainRedirectAnalyzer()

    try:
        results = analyzer.run_comprehensive_analysis()
        analyzer.generate_technical_analysis()
        analyzer.save_results()

        print(f"\n🎉 分析完成！")
        return 0

    except KeyboardInterrupt:
        print(f"\n⏹️ 分析被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
