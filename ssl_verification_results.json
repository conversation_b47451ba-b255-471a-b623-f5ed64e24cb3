{"www.huhhothome.cn_cert": {"success": true, "certificate": {"subject": [[["commonName", "www.huhhothome.cn"]]], "issuer": [[["countryName", "US"]], [["organizationName", "DigiCert Inc"]], [["organizationalUnitName", "www.digicert.com"]], [["commonName", "Encryption Everywhere DV TLS CA - G2"]]], "version": 3, "serialNumber": "012778AA72BAC4E4A3B8F730C2B2516E", "notBefore": "Jun 14 00:00:00 2025 GMT", "notAfter": "Jun 13 23:59:59 2026 GMT", "subjectAltName": [["DNS", "www.huhhothome.cn"], ["DNS", "huhhothome.cn"]], "OCSP": ["http://ocsp.digicert.com"], "caIssuers": ["http://cacerts.digicert.com/EncryptionEverywhereDVTLSCA-G2.crt"]}, "cipher": ["ECDHE-RSA-AES256-GCM-SHA384", "TLSv1.2", 256], "tls_version": "TLSv1.2", "subject": {"commonName": "www.huhhothome.cn"}, "issuer": {"countryName": "US", "organizationName": "DigiCert Inc", "organizationalUnitName": "www.digicert.com", "commonName": "Encryption Everywhere DV TLS CA - G2"}, "san": [["DNS", "www.huhhothome.cn"], ["DNS", "huhhothome.cn"]], "not_before": "Jun 14 00:00:00 2025 GMT", "not_after": "Jun 13 23:59:59 2026 GMT", "serial_number": "012778AA72BAC4E4A3B8F730C2B2516E", "version": 3}, "www.huhhothome.cn_supported_domains": ["www.huhhothome.cn", "huhhothome.cn"], "api.huhhothome.cn_cert": {"success": false, "error": "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.huhhothome.cn'. (_ssl.c:997)", "error_type": "SSLCertVerificationError"}, "https://www.huhhothome.cn_verify_true": {"success": true, "status_code": 200, "headers": {"Date": "Fri, 01 Aug 2025 13:58:58 GMT", "Content-Type": "text/html", "Content-Length": "424", "Connection": "keep-alive", "Server": "nginx", "Last-Modified": "Fri, 14 Jul 2023 06:20:13 GMT", "ETag": "\"64b0e91d-1a8\"", "P3P": "CAO PSA OUR", "Accept-Ranges": "bytes"}, "url": "https://www.huhhothome.cn/", "verify_used": true}, "https://www.huhhothome.cn_verify_false": {"success": true, "status_code": 200, "headers": {"Date": "Fri, 01 Aug 2025 13:58:58 GMT", "Content-Type": "text/html", "Content-Length": "424", "Connection": "keep-alive", "Server": "nginx", "Last-Modified": "Fri, 14 Jul 2023 06:20:13 GMT", "ETag": "\"64b0e91d-1a8\"", "P3P": "CAO PSA OUR", "Accept-Ranges": "bytes"}, "url": "https://www.huhhothome.cn/", "verify_used": false}, "https://api.huhhothome.cn_verify_true": {"success": false, "error": "HTTPSConnectionPool(host='api.huhhothome.cn', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, \"[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.huhhothome.cn'. (_ssl.c:997)\")))", "error_type": "SSLError", "verify_used": true}, "https://api.huhhothome.cn_verify_false": {"success": true, "status_code": 200, "headers": {"Date": "Fri, 01 Aug 2025 13:58:58 GMT", "Content-Type": "text/html", "Content-Length": "424", "Connection": "keep-alive", "Server": "nginx", "Last-Modified": "Fri, 14 Jul 2023 06:20:13 GMT", "ETag": "\"64b0e91d-1a8\"", "P3P": "CAO PSA OUR", "Accept-Ranges": "bytes"}, "url": "https://www.huhhothome.cn/", "verify_used": false}, "ssl_manager_config": {"housing_monitor": {"www.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}, "api.huhhothome.cn": {"should_verify": false, "requests_verify": false, "aiohttp_ssl": "<class 'bool'>", "ssl_enabled": true}}, "grab_executor": {"www.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}, "api.huhhothome.cn": {"should_verify": false, "requests_verify": false, "aiohttp_ssl": "<class 'bool'>", "ssl_enabled": true}}, "cookie_tester": {"www.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}, "api.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}}, "login_helper": {"www.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}, "api.huhhothome.cn": {"should_verify": true, "requests_verify": true, "aiohttp_ssl": "<class 'ssl.SSLContext'>", "ssl_enabled": true}}}}