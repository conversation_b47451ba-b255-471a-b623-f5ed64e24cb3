#!/usr/bin/env python3
"""
SSL域名验证测试脚本
验证 api.huhhothome.cn 和 www.huhhothome.cn 的SSL证书关系
"""

import ssl
import socket
import requests
import json
import sys
import os
from datetime import datetime
from urllib.parse import urlparse
import subprocess

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ssl_manager import get_ssl_manager
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    SSL_MANAGER_AVAILABLE = False
    print("⚠️ SSL管理器不可用，将使用基础SSL验证")

class SSLDomainVerifier:
    """SSL域名验证器"""
    
    def __init__(self):
        self.results = {}
        self.ssl_manager = None
        if SSL_MANAGER_AVAILABLE:
            try:
                self.ssl_manager = get_ssl_manager()
            except Exception as e:
                print(f"⚠️ SSL管理器初始化失败: {e}")
    
    def get_ssl_certificate_info(self, hostname, port=443):
        """获取SSL证书信息"""
        try:
            print(f"🔍 正在检查 {hostname}:{port} 的SSL证书...")
            
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 连接并获取证书
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    version = ssock.version()
                    
            return {
                'success': True,
                'certificate': cert,
                'cipher': cipher,
                'tls_version': version,
                'subject': dict(x[0] for x in cert['subject']),
                'issuer': dict(x[0] for x in cert['issuer']),
                'san': cert.get('subjectAltName', []),
                'not_before': cert['notBefore'],
                'not_after': cert['notAfter'],
                'serial_number': cert['serialNumber'],
                'version': cert['version']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def test_ssl_connection(self, url, verify=True):
        """测试SSL连接"""
        try:
            print(f"🌐 测试SSL连接: {url} (verify={verify})")
            
            response = requests.get(
                url, 
                verify=verify, 
                timeout=10,
                headers={'User-Agent': 'SSL-Verification-Test/1.0'}
            )
            
            return {
                'success': True,
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'url': response.url,
                'verify_used': verify
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__,
                'verify_used': verify
            }
    
    def analyze_certificate_domains(self, cert_info):
        """分析证书支持的域名"""
        if not cert_info.get('success'):
            return []
        
        domains = []
        cert = cert_info['certificate']
        
        # 从subject获取CN
        subject = cert_info['subject']
        if 'commonName' in subject:
            domains.append(subject['commonName'])
        
        # 从SAN获取所有域名
        san_list = cert_info['san']
        for san_type, san_value in san_list:
            if san_type == 'DNS':
                domains.append(san_value)
        
        return list(set(domains))  # 去重
    
    def test_current_ssl_manager_config(self):
        """测试当前SSL管理器配置"""
        if not self.ssl_manager:
            return {'error': 'SSL管理器不可用'}
        
        results = {}
        
        # 测试不同模块的SSL配置
        modules = ['housing_monitor', 'grab_executor', 'cookie_tester', 'login_helper']
        domains = ['www.huhhothome.cn', 'api.huhhothome.cn']
        
        for module in modules:
            results[module] = {}
            for domain in domains:
                should_verify = self.ssl_manager.should_verify_ssl(module, domain)
                verify_param = self.ssl_manager.get_requests_verify_param(module, domain)
                ssl_context = self.ssl_manager.get_aiohttp_ssl_param(module, domain)
                
                results[module][domain] = {
                    'should_verify': should_verify,
                    'requests_verify': verify_param,
                    'aiohttp_ssl': str(type(ssl_context)),
                    'ssl_enabled': self.ssl_manager.is_ssl_enabled(module)
                }
        
        return results
    
    def run_comprehensive_test(self):
        """运行综合SSL测试"""
        print("🚀 开始SSL域名验证综合测试")
        print("=" * 60)
        
        # 1. 获取两个域名的SSL证书信息
        domains_to_test = ['www.huhhothome.cn', 'api.huhhothome.cn']
        
        for domain in domains_to_test:
            print(f"\n📋 测试域名: {domain}")
            print("-" * 40)
            
            cert_info = self.get_ssl_certificate_info(domain)
            self.results[f'{domain}_cert'] = cert_info
            
            if cert_info['success']:
                print(f"✅ SSL证书获取成功")
                print(f"   颁发者: {cert_info['issuer'].get('organizationName', 'Unknown')}")
                print(f"   有效期: {cert_info['not_before']} 至 {cert_info['not_after']}")
                print(f"   TLS版本: {cert_info['tls_version']}")
                
                # 分析支持的域名
                supported_domains = self.analyze_certificate_domains(cert_info)
                print(f"   支持域名: {', '.join(supported_domains)}")
                self.results[f'{domain}_supported_domains'] = supported_domains
                
            else:
                print(f"❌ SSL证书获取失败: {cert_info['error']}")
        
        # 2. 测试SSL连接
        test_urls = [
            'https://www.huhhothome.cn',
            'https://api.huhhothome.cn'
        ]
        
        print(f"\n🔗 SSL连接测试")
        print("-" * 40)
        
        for url in test_urls:
            # 测试启用SSL验证
            result_verify = self.test_ssl_connection(url, verify=True)
            self.results[f'{url}_verify_true'] = result_verify
            
            # 测试禁用SSL验证
            result_no_verify = self.test_ssl_connection(url, verify=False)
            self.results[f'{url}_verify_false'] = result_no_verify
            
            print(f"   {url}:")
            print(f"     verify=True:  {'✅' if result_verify['success'] else '❌'}")
            print(f"     verify=False: {'✅' if result_no_verify['success'] else '❌'}")
        
        # 3. 测试当前SSL管理器配置
        if SSL_MANAGER_AVAILABLE:
            print(f"\n⚙️ 当前SSL管理器配置测试")
            print("-" * 40)
            
            ssl_config_results = self.test_current_ssl_manager_config()
            self.results['ssl_manager_config'] = ssl_config_results
            
            for module, domains in ssl_config_results.items():
                if isinstance(domains, dict):
                    print(f"   模块 {module}:")
                    for domain, config in domains.items():
                        verify_status = "启用" if config['should_verify'] else "禁用"
                        print(f"     {domain}: SSL验证{verify_status}")
        
        # 4. 证书比较分析
        self.analyze_certificate_relationship()
        
        return self.results
    
    def analyze_certificate_relationship(self):
        """分析证书关系"""
        print(f"\n🔍 证书关系分析")
        print("-" * 40)
        
        www_cert = self.results.get('www.huhhothome.cn_cert')
        api_cert = self.results.get('api.huhhothome.cn_cert')
        
        if not (www_cert and www_cert.get('success') and api_cert and api_cert.get('success')):
            print("❌ 无法进行证书比较，某个域名的证书获取失败")
            return
        
        # 比较证书序列号
        www_serial = www_cert['certificate']['serialNumber']
        api_serial = api_cert['certificate']['serialNumber']
        
        print(f"   www.huhhothome.cn 证书序列号: {www_serial}")
        print(f"   api.huhhothome.cn 证书序列号: {api_serial}")
        
        if www_serial == api_serial:
            print("✅ 两个域名使用相同的SSL证书")
            self.results['certificate_analysis'] = {
                'same_certificate': True,
                'recommendation': '可以统一SSL验证配置'
            }
        else:
            print("⚠️ 两个域名使用不同的SSL证书")
            self.results['certificate_analysis'] = {
                'same_certificate': False,
                'recommendation': '需要分别配置SSL验证'
            }
        
        # 检查SAN列表
        www_domains = self.results.get('www.huhhothome.cn_supported_domains', [])
        api_domains = self.results.get('api.huhhothome.cn_supported_domains', [])
        
        print(f"   www证书支持域名: {www_domains}")
        print(f"   api证书支持域名: {api_domains}")
        
        # 检查api.huhhothome.cn是否在www证书的SAN中
        if 'api.huhhothome.cn' in www_domains:
            print("✅ www.huhhothome.cn的证书包含api.huhhothome.cn")
            self.results['certificate_analysis']['api_in_www_cert'] = True
        else:
            print("❌ www.huhhothome.cn的证书不包含api.huhhothome.cn")
            self.results['certificate_analysis']['api_in_www_cert'] = False
    
    def generate_recommendations(self):
        """生成配置建议"""
        print(f"\n💡 配置建议")
        print("-" * 40)
        
        cert_analysis = self.results.get('certificate_analysis', {})
        
        if cert_analysis.get('same_certificate') or cert_analysis.get('api_in_www_cert'):
            print("✅ 建议统一SSL配置:")
            print("   - 将api.huhhothome.cn的SSL验证设置为True")
            print("   - 移除SSL配置中的特殊例外处理")
            print("   - 更新配置注释，说明域名关系")
        else:
            print("⚠️ 建议保持当前配置:")
            print("   - api.huhhothome.cn可能需要特殊SSL处理")
            print("   - 建议进一步调查证书配置")
    
    def save_results(self, filename='ssl_verification_results.json'):
        """保存测试结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"\n💾 测试结果已保存到: {filename}")

def main():
    """主函数"""
    print("🔐 SSL域名验证测试工具")
    print("=" * 60)
    
    verifier = SSLDomainVerifier()
    
    try:
        results = verifier.run_comprehensive_test()
        verifier.generate_recommendations()
        verifier.save_results()
        
        print(f"\n🎉 测试完成！")
        return 0
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
