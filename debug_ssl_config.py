#!/usr/bin/env python3
"""
调试SSL配置问题
检查配置管理器和SSL管理器的状态
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ssl_manager import get_ssl_manager
    from config.unified_config import get_config
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    SSL_MANAGER_AVAILABLE = False
    print("❌ SSL管理器不可用")
    sys.exit(1)

def debug_ssl_config():
    """调试SSL配置"""
    print("🔍 SSL配置调试")
    print("=" * 60)
    
    # 获取配置管理器
    config_manager = get_config()
    print(f"✅ 配置管理器获取成功: {type(config_manager)}")
    
    # 检查ssl_config是否存在
    ssl_config_from_manager = config_manager.get('ssl_config', {})
    print(f"📋 从配置管理器获取的ssl_config: {bool(ssl_config_from_manager)}")
    
    if ssl_config_from_manager:
        print("   ssl_config内容:")
        print(json.dumps(ssl_config_from_manager, indent=4, ensure_ascii=False))
    else:
        print("   ❌ ssl_config为空或不存在")
    
    # 获取SSL管理器
    ssl_manager = get_ssl_manager(config_manager)
    print(f"✅ SSL管理器获取成功: {type(ssl_manager)}")
    
    # 检查SSL管理器获取的配置
    ssl_config_from_ssl_manager = ssl_manager.get_ssl_config()
    print(f"📋 从SSL管理器获取的配置:")
    
    # 检查关键模块配置
    modules = ssl_config_from_ssl_manager.get('modules', {})
    
    for module_name in ['cookie_tester', 'login_helper', 'notifications']:
        module_config = modules.get(module_name, {})
        domains = module_config.get('domains', {})
        
        print(f"\n   {module_name}:")
        print(f"     enabled: {module_config.get('enabled', 'default')}")
        print(f"     verify: {module_config.get('verify', 'default')}")
        print(f"     domains: {list(domains.keys())}")
        
        for domain in ['www.huhhothome.cn', 'api.huhhothome.cn']:
            domain_config = domains.get(domain, {})
            if domain_config:
                print(f"       {domain}: verify={domain_config.get('verify', 'default')}")
            else:
                print(f"       {domain}: 无配置")
    
    # 测试should_verify_ssl方法
    print(f"\n🧪 测试should_verify_ssl方法:")
    test_cases = [
        ('cookie_tester', 'www.huhhothome.cn'),
        ('cookie_tester', 'api.huhhothome.cn'),
        ('login_helper', 'www.huhhothome.cn'),
        ('login_helper', 'api.huhhothome.cn'),
    ]
    
    for module, domain in test_cases:
        result = ssl_manager.should_verify_ssl(module, domain)
        print(f"   {module} + {domain}: {result}")
    
    # 检查配置管理器的所有配置
    print(f"\n📋 配置管理器所有配置键:")
    all_config = config_manager.get_all_config()
    for key in sorted(all_config.keys()):
        print(f"   {key}: {type(all_config[key])}")
    
    return {
        'ssl_config_exists': bool(ssl_config_from_manager),
        'ssl_config_content': ssl_config_from_manager,
        'ssl_manager_config': ssl_config_from_ssl_manager
    }

def force_update_ssl_config():
    """强制更新SSL配置"""
    print(f"\n🔧 强制更新SSL配置")
    print("-" * 40)
    
    config_manager = get_config()
    
    # 构建正确的SSL配置
    correct_ssl_config = {
        "enabled": True,
        "verify_certificates": True,
        "modules": {
            "housing_monitor": {
                "enabled": True,
                "verify": True,
                "domains": {
                    "www.huhhothome.cn": {"verify": True},
                    "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名"}
                }
            },
            "grab_executor": {
                "enabled": True,
                "verify": True,
                "domains": {
                    "www.huhhothome.cn": {"verify": True},
                    "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名"}
                }
            },
            "cookie_tester": {
                "enabled": True,
                "verify": True,
                "domains": {
                    "www.huhhothome.cn": {"verify": True},
                    "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名，已通过技术分析确认"}
                }
            },
            "login_helper": {
                "enabled": True,
                "verify": True,
                "domains": {
                    "www.huhhothome.cn": {"verify": True},
                    "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名，已通过技术分析确认"}
                }
            },
            "notifications": {
                "enabled": False,  # 通知器禁用SSL验证
                "verify": False,
                "domains": {
                    "api.day.app": {"verify": False},
                    "wxpusher.zjiecode.com": {"verify": False},
                    "push.i-i.me": {"verify": False},
                    "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名，已通过技术分析确认"}
                }
            }
        }
    }
    
    # 直接设置到配置管理器
    config_manager.set('ssl_config', correct_ssl_config)
    
    print("✅ SSL配置已强制更新")
    
    # 验证更新结果
    updated_config = config_manager.get('ssl_config', {})
    if updated_config:
        print("✅ 配置更新验证成功")
        
        # 测试关键配置
        modules = updated_config.get('modules', {})
        for module_name in ['cookie_tester', 'login_helper']:
            api_config = modules.get(module_name, {}).get('domains', {}).get('api.huhhothome.cn', {})
            api_verify = api_config.get('verify', True)
            print(f"   {module_name}.api.huhhothome.cn.verify = {api_verify}")
    else:
        print("❌ 配置更新验证失败")
    
    return updated_config

def test_updated_ssl_manager():
    """测试更新后的SSL管理器"""
    print(f"\n🧪 测试更新后的SSL管理器")
    print("-" * 40)
    
    config_manager = get_config()
    ssl_manager = get_ssl_manager(config_manager)
    
    # 清除SSL上下文缓存
    ssl_manager.clear_ssl_cache()
    
    test_cases = [
        ('cookie_tester', 'www.huhhothome.cn', True),
        ('cookie_tester', 'api.huhhothome.cn', False),
        ('login_helper', 'www.huhhothome.cn', True),
        ('login_helper', 'api.huhhothome.cn', False),
        ('notifications', 'api.huhhothome.cn', False),
    ]
    
    all_correct = True
    
    for module, domain, expected in test_cases:
        actual = ssl_manager.should_verify_ssl(module, domain)
        status = "✅" if actual == expected else "❌"
        print(f"   {status} {module} + {domain}: {actual} (期望: {expected})")
        
        if actual != expected:
            all_correct = False
    
    return all_correct

def main():
    """主函数"""
    if not SSL_MANAGER_AVAILABLE:
        print("❌ SSL管理器不可用")
        return 1
    
    try:
        # 1. 调试当前配置
        debug_result = debug_ssl_config()
        
        # 2. 如果配置有问题，强制更新
        if not debug_result['ssl_config_exists']:
            print(f"\n⚠️ 检测到ssl_config不存在，执行强制更新")
            force_update_ssl_config()
        else:
            # 检查配置是否正确
            ssl_config = debug_result['ssl_config_content']
            cookie_tester_api = ssl_config.get('modules', {}).get('cookie_tester', {}).get('domains', {}).get('api.huhhothome.cn', {})
            
            if cookie_tester_api.get('verify', True) != False:
                print(f"\n⚠️ 检测到ssl_config不正确，执行强制更新")
                force_update_ssl_config()
        
        # 3. 测试更新后的SSL管理器
        success = test_updated_ssl_manager()
        
        if success:
            print(f"\n🎉 SSL配置调试和修复完成！")
            return 0
        else:
            print(f"\n❌ SSL配置仍有问题")
            return 1
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
