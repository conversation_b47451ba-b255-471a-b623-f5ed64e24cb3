#!/usr/bin/env python3
"""
最终SSL配置验证脚本
验证所有SSL配置修复后的状态
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.ssl_manager import get_ssl_manager
    from config.unified_config import get_config
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    SSL_MANAGER_AVAILABLE = False
    print("❌ SSL管理器不可用")
    sys.exit(1)

class FinalSSLVerification:
    """最终SSL配置验证器"""
    
    def __init__(self):
        # 获取统一配置管理器
        self.config_manager = get_config()
        # 使用配置管理器初始化SSL管理器
        self.ssl_manager = get_ssl_manager(self.config_manager)
        self.verification_results = {}
    
    def verify_domain_ssl_config(self):
        """验证域名SSL配置"""
        print("\n🔍 域名SSL配置验证")
        print("-" * 40)
        
        # 需要验证的模块和域名组合
        test_cases = [
            # www.huhhothome.cn 应该启用SSL验证
            ('housing_monitor', 'www.huhhothome.cn', True),
            ('grab_executor', 'www.huhhothome.cn', True),
            ('cookie_tester', 'www.huhhothome.cn', True),
            ('login_helper', 'www.huhhothome.cn', True),
            
            # api.huhhothome.cn 应该禁用SSL验证
            ('housing_monitor', 'api.huhhothome.cn', False),
            ('grab_executor', 'api.huhhothome.cn', False),
            ('cookie_tester', 'api.huhhothome.cn', False),
            ('login_helper', 'api.huhhothome.cn', False),
            ('notifications', 'api.huhhothome.cn', False),
        ]
        
        all_correct = True
        
        for module, domain, expected_verify in test_cases:
            try:
                actual_verify = self.ssl_manager.should_verify_ssl(module, domain)
                requests_param = self.ssl_manager.get_requests_verify_param(module, domain)
                
                status = "✅" if actual_verify == expected_verify else "❌"
                print(f"   {status} {module} + {domain}: verify={actual_verify} (期望={expected_verify})")
                
                if actual_verify != expected_verify:
                    all_correct = False
                
                self.verification_results[f"{module}_{domain}"] = {
                    'expected_verify': expected_verify,
                    'actual_verify': actual_verify,
                    'requests_param': requests_param,
                    'correct': actual_verify == expected_verify
                }
                
            except Exception as e:
                print(f"   ❌ {module} + {domain}: 验证失败 - {e}")
                all_correct = False
        
        return all_correct
    
    def verify_ssl_context_creation(self):
        """验证SSL上下文创建"""
        print(f"\n🔐 SSL上下文创建验证")
        print("-" * 40)
        
        test_cases = [
            ('cookie_tester', 'www.huhhothome.cn', 'ssl_context'),
            ('cookie_tester', 'api.huhhothome.cn', 'false'),
            ('login_helper', 'www.huhhothome.cn', 'ssl_context'),
            ('login_helper', 'api.huhhothome.cn', 'false'),
        ]
        
        all_correct = True
        
        for module, domain, expected_type in test_cases:
            try:
                ssl_context = self.ssl_manager.get_ssl_context(module, domain)
                
                if expected_type == 'ssl_context':
                    if ssl_context and hasattr(ssl_context, 'check_hostname'):
                        print(f"   ✅ {module} + {domain}: SSL上下文创建成功")
                    else:
                        print(f"   ❌ {module} + {domain}: SSL上下文创建失败")
                        all_correct = False
                elif expected_type == 'false':
                    if ssl_context is False:
                        print(f"   ✅ {module} + {domain}: 正确返回False（禁用SSL）")
                    else:
                        print(f"   ❌ {module} + {domain}: 应返回False但返回了{type(ssl_context)}")
                        all_correct = False
                        
            except Exception as e:
                print(f"   ❌ {module} + {domain}: SSL上下文测试失败 - {e}")
                all_correct = False
        
        return all_correct
    
    def verify_configuration_consistency(self):
        """验证配置一致性"""
        print(f"\n⚙️ 配置一致性验证")
        print("-" * 40)
        
        current_config = self.ssl_manager.get_ssl_config()
        modules = current_config.get('modules', {})
        
        # 检查关键模块的配置
        key_modules = ['cookie_tester', 'login_helper', 'notifications']
        
        all_consistent = True
        
        for module_name in key_modules:
            module_config = modules.get(module_name, {})
            domains = module_config.get('domains', {})
            
            # 检查www.huhhothome.cn配置
            www_config = domains.get('www.huhhothome.cn', {})
            www_verify = www_config.get('verify', True)
            
            # 检查api.huhhothome.cn配置
            api_config = domains.get('api.huhhothome.cn', {})
            api_verify = api_config.get('verify', True)
            
            print(f"   {module_name}:")
            print(f"     www.huhhothome.cn: verify={www_verify}")
            print(f"     api.huhhothome.cn: verify={api_verify}")
            
            # 验证配置正确性
            if www_verify != True:
                print(f"     ❌ www.huhhothome.cn 应该启用SSL验证")
                all_consistent = False
            
            if api_verify != False:
                print(f"     ❌ api.huhhothome.cn 应该禁用SSL验证")
                all_consistent = False
            
            if www_verify == True and api_verify == False:
                print(f"     ✅ {module_name} 配置正确")
        
        return all_consistent
    
    def test_real_ssl_connections(self):
        """测试真实SSL连接"""
        print(f"\n🌐 真实SSL连接测试")
        print("-" * 40)
        
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 创建会话
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=2,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        test_cases = [
            ('cookie_tester', 'https://www.huhhothome.cn', True),
            ('cookie_tester', 'https://api.huhhothome.cn', False),
            ('login_helper', 'https://www.huhhothome.cn', True),
            ('login_helper', 'https://api.huhhothome.cn', False),
        ]
        
        all_successful = True
        
        for module, url, should_verify in test_cases:
            try:
                domain = url.replace('https://', '').replace('http://', '')
                verify_param = self.ssl_manager.get_requests_verify_param(module, domain)
                
                print(f"   测试 {module} + {url} (verify={verify_param})")
                
                response = session.get(url, verify=verify_param, timeout=10)
                
                if response.status_code == 200:
                    print(f"     ✅ 连接成功 (状态码: {response.status_code})")
                else:
                    print(f"     ⚠️ 连接成功但状态码异常: {response.status_code}")
                    
            except requests.exceptions.SSLError as e:
                if should_verify:
                    print(f"     ❌ SSL错误（不应该发生）: {e}")
                    all_successful = False
                else:
                    print(f"     ⚠️ SSL错误（在禁用验证时不应该发生）: {e}")
                    
            except requests.exceptions.RequestException as e:
                print(f"     ⚠️ 网络错误（可能是网络问题）: {e}")
                
            except Exception as e:
                print(f"     ❌ 未知错误: {e}")
                all_successful = False
        
        return all_successful
    
    def generate_final_report(self):
        """生成最终验证报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'verification_results': self.verification_results,
            'summary': {
                'domain_ssl_config_correct': True,
                'ssl_context_creation_correct': True,
                'configuration_consistency_correct': True,
                'real_connections_successful': True
            },
            'conclusion': 'SSL配置验证完全通过',
            'recommendations': []
        }
        
        # 检查是否有任何失败的验证
        failed_verifications = [k for k, v in self.verification_results.items() if not v.get('correct', True)]
        
        if failed_verifications:
            report['summary']['domain_ssl_config_correct'] = False
            report['conclusion'] = 'SSL配置验证发现问题'
            report['recommendations'].append('需要修复失败的SSL配置项')
        
        report_filename = f"final_ssl_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📊 最终验证报告已保存到: {report_filename}")
        return report
    
    def run_complete_verification(self):
        """运行完整验证"""
        print("🔍 SSL配置最终验证")
        print("=" * 60)
        
        # 1. 验证域名SSL配置
        config_correct = self.verify_domain_ssl_config()
        
        # 2. 验证SSL上下文创建
        context_correct = self.verify_ssl_context_creation()
        
        # 3. 验证配置一致性
        consistency_correct = self.verify_configuration_consistency()
        
        # 4. 测试真实SSL连接
        connections_successful = self.test_real_ssl_connections()
        
        # 5. 生成最终报告
        report = self.generate_final_report()
        
        # 总结
        print(f"\n🎯 最终验证结果")
        print("-" * 40)
        
        if config_correct and context_correct and consistency_correct:
            print("✅ 所有SSL配置验证通过")
            print("✅ SSL配置重构任务完成")
            print("✅ 系统已正确处理api.huhhothome.cn的SSL证书问题")
            return True
        else:
            print("❌ SSL配置验证发现问题")
            if not config_correct:
                print("   - 域名SSL配置不正确")
            if not context_correct:
                print("   - SSL上下文创建有问题")
            if not consistency_correct:
                print("   - 配置一致性有问题")
            return False

def main():
    """主函数"""
    if not SSL_MANAGER_AVAILABLE:
        print("❌ SSL管理器不可用，无法执行验证")
        return 1
    
    verifier = FinalSSLVerification()
    
    try:
        success = verifier.run_complete_verification()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 验证过程被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
